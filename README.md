# AI 对冲基金 - 智能投资决策系统

这是一个基于人工智能的对冲基金概念验证项目，旨在探索使用大语言模型(LLM)进行智能投资决策。该系统集成了多个专业化的AI代理，模拟知名投资者的投资策略，并结合多源数据分析来做出投资决策。

**⚠️ 重要声明：本项目仅供教育和研究目的使用，不适用于实际交易或投资。**

## 🎯 系统特色

- **🤖 多代理协作**：21个专业化AI代理，模拟知名投资者策略和专业分析师
- **📊 多维度分析**：基本面、技术面、情绪面、估值、新闻、社交媒体分析
- **📰 本地数据优先**：支持本地新闻、社交媒体、金融数据，避免API限制
- **🧠 LLM智能分析**：支持多种LLM提供商，包括免费和付费模型
- **📈 完整回测系统**：历史数据回测，性能评估和可视化
- **🔄 反思学习机制**：持续改进决策质量和准确性跟踪
- **🌐 Web界面**：直观的用户界面和实时进度跟踪
- **💾 数据缓存系统**：智能缓存机制，提高回测效率

<img width="1042" alt="Screenshot 2025-03-22 at 6 19 07 PM" src="https://github.com/user-attachments/assets/cbae3dcf-b571-490d-b0ad-3f0f035ac0d4" />

## 🏛️ 投资大师代理团队

### 价值投资派
- **Warren Buffett** - 奥马哈的先知，寻找具有经济护城河的优质公司
- **Charlie Munger** - 巴菲特的合伙人，运用多元思维模型
- **Ben Graham** - 价值投资之父，坚持安全边际原则
- **Michael Burry** - 《大空头》逆势投资者，寻找深度价值

### 成长投资派
- **Phil Fisher** - 成长型投资先驱，深度"小道消息"研究
- **Peter Lynch** - 实用投资者，寻找日常生活中的"十倍股"
- **Cathie Wood** - 创新投资女王，专注颠覆性技术

### 激进投资派
- **Bill Ackman** - 激进投资者，推动企业变革
- **Stanley Druckenmiller** - 宏观投资传奇，寻找不对称机会

### 估值专家
- **Aswath Damodaran** - 纽约大学估值专家，严格的DCF分析

### 专业分析师（传统）
- **基本面分析师** - 财务数据深度分析
- **技术分析师** - 价格趋势和技术指标分析
- **情绪分析师** - 市场情绪和投资者行为分析
- **估值分析师** - 内在价值计算和相对估值
- **事实新闻分析师** - 客观新闻事件分析
- **主观新闻分析师** - 新闻情感和影响分析

### 新一代智能分析师（TradingAgents）
- **市场分析师 (TA)** - 基于LLM的智能市场环境分析
- **新闻分析师 (TA)** - 基于LLM的新闻情感和事件影响分析
- **社交媒体分析师 (TA)** - 基于LLM的社交情感和公众舆论分析
- **基本面分析师 (TA)** - 基于LLM的财务数据智能分析

### 系统管理代理
- **投资组合管理器** - 最终决策制定和交易执行
- **风险管理器** - 投资组合风险评估和控制
- **反思分析师** - 决策质量分析和持续改进

[![Twitter Follow](https://img.shields.io/twitter/follow/virattt?style=social)](https://twitter.com/virattt)

## ⚠️ 免责声明

本项目**仅供教育和研究目的**。

- ❌ 不适用于实际交易或投资
- ❌ 不提供投资建议或保证
- ❌ 创建者对财务损失不承担责任
- ✅ 投资决策请咨询专业财务顾问
- ⚠️ 过去的表现不代表未来的结果

使用本软件即表示您同意仅将其用于学习和研究目的。

## 📚 目录
- [🎯 系统特色](#-系统特色)
- [🏛️ 投资大师代理团队](#️-投资大师代理团队)
- [🚀 快速开始](#-快速开始)
  - [环境配置](#环境配置)
  - [基本使用](#基本使用)
  - [回测系统](#回测系统)
- [📊 本地数据配置](#-本地数据配置)
  - [本地新闻数据系统](#本地新闻数据系统)
  - [本地社交媒体数据](#本地社交媒体数据)
  - [本地金融数据](#本地金融数据)
  - [支持的股票代码](#支持的股票代码)
- [🤖 LLM模型配置](#-llm模型配置)
  - [支持的模型提供商](#支持的模型提供商)
  - [API密钥配置](#api密钥配置)
  - [模型选择建议](#模型选择建议)
- [📈 回测功能详解](#-回测功能详解)
  - [基本回测命令](#基本回测命令)
  - [命令行参数详解](#命令行参数详解)
  - [高级功能](#高级功能)
  - [推理日志和准确性跟踪](#推理日志和准确性跟踪)
- [🔧 系统架构](#-系统架构)
- [📁 项目结构](#-项目结构)
- [🛠️ 故障排除](#️-故障排除)
- [❓ 常见问题](#-常见问题)
- [🤝 贡献指南](#-贡献指南)
- [📄 许可证](#-许可证)

## 🚀 快速开始

### 环境配置

#### 使用Poetry（推荐）

1. **克隆仓库**
```bash
git clone https://github.com/virattt/ai-hedge-fund.git
cd ai-hedge-fund
```

2. **安装Poetry**
```bash
curl -sSL https://install.python-poetry.org | python3 -
```

3. **安装依赖**
```bash
poetry install
```

4. **配置环境变量**
```bash
cp .env.example .env
# 编辑.env文件，添加必要的API密钥
```

#### 使用Docker

1. **构建Docker镜像**
```bash
# Linux/Mac
./run.sh build

# Windows
run.bat build
```

2. **配置环境变量**
```bash
cp .env.example .env
# 编辑.env文件，添加API密钥
```

### 基本使用

#### 运行AI对冲基金系统

```bash
# 使用Poetry
poetry run python src/main.py --ticker AAPL,MSFT,NVDA

# 使用Docker (Linux/Mac)
./run.sh --ticker AAPL,MSFT,NVDA main

# 使用Docker (Windows)
run.bat --ticker AAPL,MSFT,NVDA main
```

#### 显示推理过程

```bash
poetry run python src/main.py --ticker AAPL --show-reasoning
```

#### 指定时间范围

```bash
poetry run python src/main.py --ticker AAPL --start-date 2024-01-01 --end-date 2024-03-01
```

### 回测系统

#### 基本回测

```bash
# 使用Poetry
poetry run python src/backtester.py --ticker AAPL,MSFT,NVDA

# 使用Docker
./run.sh --ticker AAPL,MSFT,NVDA backtest
```

#### 高级回测功能

```bash
# 启用推理日志和准确性跟踪
python src/backtester.py \
  --tickers AAPL \
  --start-date 2024-01-01 \
  --end-date 2024-12-31 \
  --track-accuracy \
  --save-reasoning \
  --save-input-data

# 使用本地新闻数据（推荐，避免API限制）
python src/backtester.py \
  --tickers NVDA \
  --start-date 2024-01-02 \
  --end-date 2024-12-31 \
  --use-local-news \
  --news-sources alpha_vantage \
  --news-time-offset 1 \
  --track-accuracy \
  --save-reasoning

# 使用本地社交媒体数据
python src/backtester.py \
  --tickers AAPL \
  --start-date 2024-01-01 \
  --end-date 2024-06-01 \
  --use-local-social-media \
  --social-media-sources reddit \
  --track-accuracy
```

## 📊 本地数据配置

### 本地新闻数据系统

系统支持使用本地新闻数据，避免API速率限制，提高回测速度和数据一致性。

#### 支持的股票和数据源

| 股票代码 | Alpha Vantage | NewsAPI | Finnhub | 社交媒体 | 金融数据 | 数据范围 |
|---------|---------------|---------|---------|----------|----------|----------|
| AAPL | ✅ | ✅ | ✅ | ✅ | ✅ | 2024-2025 |
| MSFT | ✅ | ✅ | ✅ | ✅ | ✅ | 2024-2025 |
| NVDA | ✅ | ❌ | ❌ | ✅ | ✅ | 2024-2025 |
| GOOGL | ✅ | ❌ | ❌ | ✅ | ❌ | 2024-2025 |
| TSLA | ✅ | ❌ | ❌ | ✅ | ❌ | 2024-2025 |

#### 🔄 新闻数据切换策略

##### 单股票回测
```bash
# AAPL - 使用所有可用源（最佳数据覆盖）
python src/backtester.py \
  --tickers AAPL \
  --use-local-news \
  --news-sources alpha_vantage,newsapi,finnhub

# MSFT - 使用双源配置（平衡质量和数量）
python src/backtester.py \
  --tickers MSFT \
  --use-local-news \
  --news-sources alpha_vantage,newsapi

# NVDA - 使用单源配置（唯一可用源）
python src/backtester.py \
  --tickers NVDA \
  --use-local-news \
  --news-sources alpha_vantage
```

##### 多股票回测智能切换
```bash
# 系统会自动为每个股票选择最佳可用新闻源
python src/backtester.py \
  --tickers AAPL,MSFT,NVDA \
  --use-local-news \
  --news-sources alpha_vantage,newsapi,finnhub

# 实际执行时的新闻源分配：
# AAPL: alpha_vantage + newsapi + finnhub
# MSFT: alpha_vantage + newsapi
# NVDA: alpha_vantage
```

##### 新闻源质量对比

| 新闻源 | 数据质量 | 更新频率 | 覆盖范围 | 适用场景 |
|--------|----------|----------|----------|----------|
| **Alpha Vantage** | ⭐⭐⭐⭐⭐ | 高 | 广泛 | 所有股票的首选源 |
| **NewsAPI** | ⭐⭐⭐⭐ | 很高 | 中等 | 补充实时新闻 |
| **Finnhub** | ⭐⭐⭐⭐ | 高 | 金融专业 | 专业金融分析 |

#### 配置本地新闻数据

**方法1：环境变量**
```bash
export NEWS_USE_LOCAL_DATA=true
export NEWS_SOURCES=alpha_vantage,newsapi,finnhub
export NEWS_TIME_OFFSET_DAYS=1
```

**方法2：命令行参数**
```bash
python src/backtester.py \
  --use-local-news \
  --news-sources alpha_vantage,newsapi \
  --news-time-offset 1
```

**方法3：配置文件**
```json
{
  "default_settings": {
    "use_local_data": true,
    "time_offset_days": 1,
    "selected_sources": ["alpha_vantage", "newsapi", "finnhub"]
  }
}
```

#### 本地数据目录结构
```
项目根目录/
├── AAPL_alpha_news/           # AAPL Alpha Vantage新闻
│   ├── alpha_news_2024-01-01.json
│   └── ...
├── AAPL_news_api_news/        # AAPL NewsAPI新闻
│   ├── news_api_2024-01-01.json
│   └── ...
├── MSFT_alpha_news/           # MSFT Alpha Vantage新闻
├── NVDA_alpha_news/           # NVDA Alpha Vantage新闻
└── news_config.json           # 配置文件
```

### 本地社交媒体数据

系统支持本地社交媒体数据，避免API限制和提高数据一致性。

#### 数据目录结构
```
social_media_data/
├── AAPL_social_media/
│   ├── AAPL_2024-01-01.json
│   ├── AAPL_2024-01-02.json
│   └── ...
├── MSFT_social_media/
│   ├── MSFT_2024-01-01.json
│   └── ...
├── NVDA_social_media/
├── GOOGL_social_media/
└── TSLA_social_media/
```

#### 使用本地社交媒体数据

```bash
# 基本使用
python src/backtester.py \
  --tickers AAPL \
  --use-local-social-media \
  --social-media-sources reddit

# 多股票回测
python src/backtester.py \
  --tickers AAPL,MSFT,NVDA \
  --use-local-social-media \
  --social-media-sources reddit \
  --start-date 2024-01-01 \
  --end-date 2024-06-01
```

#### 数据文件格式
每个JSON文件包含当日的社交媒体数据：
```json
{
  "date": "2024-01-01",
  "ticker": "AAPL",
  "posts": [
    {
      "content": "Apple stock looking strong...",
      "sentiment": "positive",
      "engagement": 150,
      "source": "reddit"
    }
  ],
  "summary": {
    "total_posts": 25,
    "positive_sentiment": 0.6,
    "negative_sentiment": 0.2,
    "neutral_sentiment": 0.2
  }
}
```

### 本地金融数据

系统支持本地金融数据缓存，减少API调用并提高回测速度。

#### 数据目录结构
```
financial_data_offline/
├── AAPL_line_items/
│   ├── AAPL_line_items_2024-01-01.json
│   └── ...
├── AAPL_market_cap/
├── AAPL_prices/
├── MSFT_line_items/
├── MSFT_market_cap/
├── MSFT_prices/
└── NVDA_line_items/
```

#### 支持的数据类型
- **line_items**: 财务报表数据（季度更新）
- **market_cap**: 市值数据（每日更新）
- **prices**: 股价数据（每日更新）
- **insider_trades**: 内部交易数据
- **financial_metrics**: 财务指标数据

#### 自动本地数据优先策略
系统会自动优先使用本地数据，当本地数据不可用时回退到API调用：

1. **检查本地数据** - 首先查找本地缓存文件
2. **API回退** - 本地数据不存在时调用API
3. **数据缓存** - 将API获取的数据保存到本地
4. **向后兼容** - 保持与现有代码的完全兼容

#### 金融数据下载脚本
为了提高回测速度和减少API调用，系统提供了专门的数据下载脚本：

```bash
# 下载GOOGL和TSLA的所有金融数据
python download_googl_tsla_financial_data.py

# 只下载特定股票
python download_googl_tsla_financial_data.py --tickers GOOGL

# 指定时间范围
python download_googl_tsla_financial_data.py --start-date 2024-01-01 --end-date 2025-06-01

# 只下载特定数据类型
python download_googl_tsla_financial_data.py --data-types prices line_items market_cap
```

**测试下载功能：**
```bash
# 运行测试脚本验证环境配置
python test_googl_tsla_download.py

# 运行完整设置演示（推荐新用户）
python demo_googl_tsla_setup.py

# 演示模式（不实际执行，仅展示流程）
python demo_googl_tsla_setup.py --demo-mode
```

### 支持的股票代码

系统目前支持以下股票代码，每个股票都有不同程度的本地数据支持：

#### 完全支持（所有数据类型）
- **AAPL** - Apple Inc.
  - ✅ 新闻数据：Alpha Vantage + NewsAPI + Finnhub
  - ✅ 社交媒体数据：Reddit
  - ✅ 金融数据：完整财务指标和价格数据
  - 📅 数据范围：2024-01-01 至 2025-06-01

- **MSFT** - Microsoft Corporation
  - ✅ 新闻数据：Alpha Vantage + NewsAPI + Finnhub
  - ✅ 社交媒体数据：Reddit
  - ✅ 金融数据：完整财务指标和价格数据
  - 📅 数据范围：2024-01-01 至 2025-06-01

- **NVDA** - NVIDIA Corporation
  - ✅ 新闻数据：Alpha Vantage
  - ✅ 社交媒体数据：Reddit
  - ✅ 金融数据：完整财务指标和价格数据
  - 📅 数据范围：2024-01-01 至 2025-06-01

#### 扩展支持（需要下载金融数据）
- **GOOGL** - Alphabet Inc.
  - ✅ 新闻数据：Alpha Vantage
  - ✅ 社交媒体数据：Reddit
  - ✅ 金融数据：支持本地数据优先策略，可使用下载脚本预缓存
  - 📅 数据范围：2024-01-01 至 2025-06-01

- **TSLA** - Tesla Inc.
  - ✅ 新闻数据：Alpha Vantage
  - ✅ 社交媒体数据：Reddit
  - ✅ 金融数据：支持本地数据优先策略，可使用下载脚本预缓存
  - 📅 数据范围：2024-01-01 至 2025-06-01

> 📖 **详细设置指南**: 参见 [GOOGL和TSLA设置指南](docs/googl_tsla_setup_guide.md)

#### 推荐使用策略
```bash
# 最佳体验（完全本地数据）
python src/backtester.py --tickers AAPL,MSFT,NVDA

# 扩展股票回测（需要先下载金融数据）
python download_googl_tsla_financial_data.py  # 先下载数据
python src/backtester.py --tickers GOOGL,TSLA --use-local-news --use-local-social-media

# 混合模式（本地+API）
python src/backtester.py --tickers AAPL,GOOGL,TSLA

# 单股票深度分析
python src/backtester.py --tickers AAPL --use-local-news --use-local-social-media
```

#### 免费数据支持

- **AAPL、GOOGL、MSFT、NVDA、TSLA**：金融数据免费，无需API密钥
- **其他股票**：需要Financial Datasets API密钥

## 🤖 LLM模型配置

### 支持的模型提供商

| 提供商 | 模型示例 | 特点 | 配置要求 |
|--------|----------|------|----------|
| **OpenAI** | gpt-4o, gpt-4o-mini | 高质量分析，稳定性好 | OPENAI_API_KEY |
| **Groq** | llama-4-scout-17b, deepseek-v3 | 速度快，支持多密钥轮换 | GROQ_API_KEY1-4 |
| **Anthropic** | claude-3-sonnet, claude-3-haiku | 推理能力强 | ANTHROPIC_API_KEY |
| **DeepSeek** | deepseek-v3 | 成本低，中文支持好 | DEEPSEEK_API_KEY |
| **OpenRouter** | meta-llama/llama-4-scout:free | 免费模型支持 | OPENROUTER_API_KEY |
| **青云API** | claude, gemini, gpt, llama-4-scout, grok-3-mini | 国内访问稳定 | QINGYUN_API_KEY |
| **Ollama** | 本地模型 | 完全离线，无API费用 | 本地安装Ollama |

### API密钥配置

#### 基本配置
```bash
# 主要LLM提供商（推荐配置多个）
OPENAI_API_KEY=sk-...
GROQ_API_KEY1=gsk_...
GROQ_API_KEY2=gsk_...  # 多密钥轮换，避免速率限制
ANTHROPIC_API_KEY=sk-ant-...
DEEPSEEK_API_KEY=sk-...

# OpenRouter免费模型
OPENROUTER_API_KEY=sk-or-v1-...

# 青云API（新增模型支持）
QINGYUN_API_KEY=your-qingyun-api-key
```

#### 高级配置
```bash
# GROQ API密钥轮换（推荐）
GROQ_API_KEY1=your-first-groq-key
GROQ_API_KEY2=your-second-groq-key
GROQ_API_KEY3=your-third-groq-key
GROQ_API_KEY4=your-fourth-groq-key

# 青云API配置（国内用户推荐）
# 在代码中自动使用 https://api.qingyuntop.tp/v1 端点
```

### 模型选择建议

#### 按用途选择
- **高质量分析**：gpt-4o, claude-3-sonnet
- **快速回测**：llama-4-scout-17b, deepseek-v3, meta-llama/llama-4-maverick
- **成本控制**：gpt-4o-mini, deepseek-v3, grok-beta
- **离线使用**：Ollama本地模型
- **免费试用**：OpenRouter免费模型
- **新增模型**：meta-llama/llama-4-scout, meta-llama/llama-4-maverick, grok-beta (通过QingYun API)

#### 按场景选择
```bash
# 生产环境（高质量）
python src/backtester.py --model gpt-4o --provider OpenAI

# 开发测试（快速）
python src/backtester.py --model deepseek-v3 --provider Groq

# 离线环境
python src/backtester.py --ollama

# 免费试用
python src/backtester.py --model meta-llama/llama-4-scout:free --provider OpenRouter

# 新增QingYun模型
python src/backtester.py --model meta-llama/llama-4-scout --provider QingYun
python src/backtester.py --model grok-beta --provider QingYun
python src/backtester.py --model grok-3-reasoner --provider QingYun
```

### QingYun模型测试

在使用QingYun模型之前，建议先运行测试脚本验证模型可用性：

```bash
# 快速连接测试
python test_api/test_qingyun_quick.py --connection

# 快速模型测试（推荐）
python test_api/test_qingyun_quick.py

# 测试单个模型
python test_api/test_qingyun_quick.py --model "gpt-4o"

# 综合性能测试
python test_api/test_qingyun_comprehensive.py

# 查看所有可用模型
python test_api/test_qingyun_comprehensive.py --list

# 专门测试Grok-3-Reasoner模型
python test_api/test_grok_3_reasoner.py

# 测试Grok-3-Reasoner推理能力
python test_api/test_grok_3_reasoner.py --reasoning
```

**测试脚本说明**：
- `test_qingyun_quick.py`: 快速测试，验证基本功能
- `test_qingyun_comprehensive.py`: 综合测试，包含性能评估和详细报告
- `test_grok_3_reasoner.py`: Grok-3-Reasoner专用测试，包含推理能力和金融分析测试
- 详细使用指南请参考：[QingYun测试指南](docs/qingyun_testing_guide.md)

### Grok-3-Reasoner模型

Grok-3-Reasoner是QingYun API提供的高级推理模型，专门设计用于处理复杂的逻辑推理和分析任务：

#### 🎯 核心优势
- **强大推理能力**: 专门优化的推理架构，适合复杂逻辑分析
- **大数据处理**: 能够处理复杂的多代理分析场景，解决反思分析师在处理大量数据时的问题
- **金融分析专长**: 在金融市场分析和投资决策方面表现优异
- **稳定性增强**: 改进的错误处理和速率限制管理

#### 🚀 快速使用
```bash
# NVDA回测示例（推荐用法）
python src/backtester.py \
  --tickers NVDA \
  --start-date 2024-01-02 \
  --end-date 2024-12-31 \
  --use-local-news \
  --news-sources alpha_vantage \
  --track-accuracy \
  --save-reasoning
# 然后选择: [qingyun] grok-3-reasoner
```

#### 📚 详细文档
- 完整使用指南：[Grok-3-Reasoner使用指南](docs/grok_3_reasoner_guide.md)
- 包含程序化使用、最佳实践和故障排除

### QingYun API速率限制

为了防止QingYun API服务器饱和，系统自动为QingYun提供商的模型添加速率限制保护：

#### 🛡️ 自动速率限制特性
- **自动检测**：系统自动识别QingYun API模型
- **智能延迟**：每个交易日结束后等待30秒再处理下一个交易日
- **透明处理**：无需手动配置，自动启用
- **提供商隔离**：只影响QingYun模型，不影响其他提供商

#### ⏱️ 性能影响估算
```bash
# 单日回测：无影响
# 10日回测：增加4.5分钟 (9 × 30秒)
# 30日回测：增加14.5分钟 (29 × 30秒)
# 252日回测：增加2.1小时 (251 × 30秒)
```

#### 🎯 适用模型
所有QingYun API提供的模型都会自动应用30秒延迟：
- claude-3-5-sonnet-latest, claude-3-7-sonnet-latest
- gemini-2.0-flash, gemini-2.5-pro-exp-03-25
- gpt-4.5-preview, gpt-4o, gpt-3.5-turbo, o3, o4-mini
- meta-llama/llama-4-scout, meta-llama/llama-4-maverick
- grok-beta, grok-3-reasoner

#### 💡 使用建议
- **长期回测**：QingYun模型特别适合多日/多周的长期回测
- **稳定性优先**：30秒延迟确保API调用的稳定性和成功率
- **成本效益**：避免因速率限制导致的重试和失败成本

#### 🧪 测试速率限制功能
```bash
# 运行速率限制测试
python test_api/test_qingyun_rate_limiting.py

# 查看速率限制演示
python test_api/demo_qingyun_rate_limiting.py
```

## 📈 回测功能详解

### 📋 回测参数完整指南

#### 🎯 基本参数

| 参数 | 类型 | 默认值 | 说明 | 示例 |
|------|------|--------|------|------|
| `--tickers` | 字符串 | 必需 | 股票代码，多个用逗号分隔 | `AAPL,MSFT,NVDA` |
| `--start-date` | 日期 | 30天前 | 回测开始日期 | `2024-01-01` |
| `--end-date` | 日期 | 今天 | 回测结束日期 | `2024-12-31` |
| `--initial-capital` | 数字 | 100000 | 初始资金（美元） | `50000` |

#### 📰 新闻数据参数

| 参数 | 类型 | 默认值 | 说明 | 示例 |
|------|------|--------|------|------|
| `--use-local-news` | 布尔 | False | 使用本地新闻数据 | `--use-local-news` |
| `--news-sources` | 字符串 | alpha_vantage | 新闻源，多个用逗号分隔 | `alpha_vantage,newsapi,finnhub` |
| `--news-time-offset` | 数字 | 0 | 新闻时间偏移（天） | `1` |

#### 📱 社交媒体参数

| 参数 | 类型 | 默认值 | 说明 | 示例 |
|------|------|--------|------|------|
| `--use_local_social_media` | 布尔 | False | 使用本地社交媒体数据 | `--use_local_social_media` |
| `--social_media_sources` | 字符串 | reddit | 社交媒体源 | `reddit,stocktwits` |
| `--social_media_time_offset` | 数字 | 0 | 社交媒体时间偏移（小时） | `2` |

#### 🧠 LLM模型参数

| 参数 | 类型 | 默认值 | 说明 | 示例 |
|------|------|--------|------|------|
| `--model` | 字符串 | 交互选择 | 指定LLM模型 | `gpt-4o` |
| `--provider` | 字符串 | 交互选择 | 指定LLM提供商 | `OpenAI` |
| `--ollama` | 布尔 | False | 使用本地Ollama模型 | `--ollama` |

#### 👥 分析师选择参数

| 参数 | 类型 | 默认值 | 说明 | 示例 |
|------|------|--------|------|------|
| `--analysts` | 字符串 | 交互选择 | 指定分析师，多个用逗号分隔 | `warren_buffett,peter_lynch` |
| `--exclude-analysts` | 字符串 | 无 | 排除特定分析师 | `reflection_analyst` |

#### 📊 日志和跟踪参数

| 参数 | 类型 | 默认值 | 说明 | 示例 |
|------|------|--------|------|------|
| `--track-accuracy` | 布尔 | False | 启用准确性跟踪 | `--track-accuracy` |
| `--save-reasoning` | 布尔 | False | 保存推理日志 | `--save-reasoning` |
| `--save-input-data` | 布尔 | False | 保存输入数据 | `--save-input-data` |
| `--show-reasoning` | 布尔 | False | 终端显示推理过程 | `--show-reasoning` |

#### 🔧 高级参数

| 参数 | 类型 | 默认值 | 说明 | 示例 |
|------|------|--------|------|------|
| `--deterministic` | 布尔 | False | 确定性模式（可重现结果） | `--deterministic` |
| `--seed` | 数字 | 随机 | 随机种子 | `42` |
| `--experiment-id` | 字符串 | 自动生成 | 实验标识符 | `my_experiment` |
| `--max-workers` | 数字 | 4 | 最大并行工作线程数 | `8` |

### 命令行参数详解

系统提供了丰富的命令行参数来控制回测行为，以下是完整的参数说明和使用示例。

### 基本回测命令

#### 简单回测
```bash
# 基本回测
python src/backtester.py --tickers AAPL

# 多股票回测
python src/backtester.py --tickers AAPL,MSFT,NVDA

# 指定时间范围
python src/backtester.py \
  --tickers AAPL \
  --start-date 2024-01-01 \
  --end-date 2024-12-31
```

### 📚 参数使用示例

#### 🚀 快速开始示例
```bash
# 最简单的回测
python src/backtester.py --tickers AAPL

# 快速测试（短期）
python src/backtester.py \
  --tickers AAPL \
  --start-date 2024-12-01 \
  --end-date 2024-12-31

# 多股票快速回测
python src/backtester.py \
  --tickers AAPL,MSFT,NVDA \
  --start-date 2024-11-01 \
  --end-date 2024-12-31
```

#### 📰 新闻数据使用示例
```bash
# 使用本地新闻数据（推荐）
python src/backtester.py \
  --tickers NVDA \
  --use-local-news \
  --news-sources alpha_vantage \
  --news-time-offset 1

# 多源新闻数据回测
python src/backtester.py \
  --tickers AAPL \
  --use-local-news \
  --news-sources alpha_vantage,newsapi,finnhub \
  --news-time-offset 1

# API新闻数据回测（需要API密钥）
python src/backtester.py \
  --tickers MSFT \
  --news-sources alpha_vantage,newsapi
```

#### 📱 社交媒体数据示例
```bash
# 使用本地社交媒体数据
python src/backtester.py \
  --tickers AAPL \
  --use_local_social_media \
  --social_media_sources reddit \
  --social_media_time_offset 2

# 结合新闻和社交媒体数据
python src/backtester.py \
  --tickers AAPL \
  --use-local-news \
  --news-sources alpha_vantage \
  --use_local_social_media \
  --social_media_sources reddit
```

#### 🧠 LLM模型选择示例
```bash
# 指定OpenAI模型
python src/backtester.py \
  --tickers AAPL \
  --model gpt-4o \
  --provider OpenAI

# 使用Groq快速模型
python src/backtester.py \
  --tickers AAPL \
  --model deepseek-v3 \
  --provider Groq

# 使用本地Ollama模型
python src/backtester.py \
  --tickers AAPL \
  --ollama

# 使用QingYun API模型
python src/backtester.py \
  --tickers AAPL \
  --model grok-3-reasoner \
  --provider QingYun
```

#### 👥 分析师选择示例
```bash
# 只使用价值投资大师
python src/backtester.py \
  --tickers AAPL \
  --analysts warren_buffett,charlie_munger,ben_graham

# 使用LLM智能分析师
python src/backtester.py \
  --tickers AAPL \
  --analysts fundamentals_analyst_ta,news_analyst,social_media_analyst

# 排除反思分析师（加快速度）
python src/backtester.py \
  --tickers AAPL \
  --exclude-analysts reflection_analyst
```

#### 📊 完整功能回测示例
```bash
# NVDA完整回测（推荐命令）
python src/backtester.py \
  --tickers NVDA \
  --start-date 2024-01-02 \
  --end-date 2024-12-31 \
  --use-local-news \
  --news-sources alpha_vantage \
  --news-time-offset 1 \
  --use_local_social_media \
  --track-accuracy \
  --save-reasoning \
  --save-input-data \
  --show-reasoning

# AAPL多源数据回测
python src/backtester.py \
  --tickers AAPL \
  --start-date 2024-01-01 \
  --end-date 2024-12-31 \
  --use-local-news \
  --news-sources alpha_vantage,newsapi,finnhub \
  --use_local_social_media \
  --track-accuracy \
  --save-reasoning \
  --model gpt-4o \
  --provider OpenAI

# 多股票综合回测
python src/backtester.py \
  --tickers AAPL,MSFT,NVDA \
  --start-date 2024-01-01 \
  --end-date 2024-12-31 \
  --use-local-news \
  --news-sources alpha_vantage,newsapi \
  --track-accuracy \
  --save-reasoning \
  --initial-capital 200000
```

#### 🔧 高级配置示例
```bash
# 确定性回测（可重现结果）
python src/backtester.py \
  --tickers AAPL \
  --deterministic \
  --seed 42 \
  --experiment-id reproducible_test

# 高性能并行回测
python src/backtester.py \
  --tickers AAPL,MSFT,NVDA,GOOGL,TSLA \
  --max-workers 8 \
  --start-date 2024-01-01 \
  --end-date 2024-12-31

# 调试模式回测
python src/backtester.py \
  --tickers AAPL \
  --show-reasoning \
  --save-reasoning \
  --save-input-data \
  --start-date 2024-12-01 \
  --end-date 2024-12-05
```

#### 🔄 如何切换不同股票的新闻数据

系统支持为不同股票使用不同的新闻源配置。以下是详细的切换方法：

##### 方法1：命令行参数切换
```bash
# 为AAPL使用所有可用新闻源
python src/backtester.py \
  --tickers AAPL \
  --use-local-news \
  --news-sources alpha_vantage,newsapi,finnhub

# 为MSFT使用Alpha Vantage和NewsAPI
python src/backtester.py \
  --tickers MSFT \
  --use-local-news \
  --news-sources alpha_vantage,newsapi

# 为NVDA只使用Alpha Vantage（推荐）
python src/backtester.py \
  --tickers NVDA \
  --use-local-news \
  --news-sources alpha_vantage

# 多股票回测时，系统会自动为每个股票选择可用的新闻源
python src/backtester.py \
  --tickers AAPL,MSFT,NVDA \
  --use-local-news \
  --news-sources alpha_vantage,newsapi,finnhub
```

##### 方法2：环境变量配置
```bash
# 设置默认新闻源
export NEWS_SOURCES=alpha_vantage,newsapi,finnhub
export NEWS_USE_LOCAL_DATA=true
export NEWS_TIME_OFFSET_DAYS=1

# 运行回测（会使用环境变量配置）
python src/backtester.py --tickers AAPL,MSFT,NVDA
```

##### 方法3：配置文件切换
创建或修改 `news_config.json` 文件：
```json
{
  "default_settings": {
    "use_local_data": true,
    "time_offset_days": 1,
    "selected_sources": ["alpha_vantage", "newsapi", "finnhub"]
  },
  "ticker_specific": {
    "AAPL": {
      "sources": ["alpha_vantage", "newsapi", "finnhub"],
      "priority": ["alpha_vantage", "newsapi", "finnhub"]
    },
    "MSFT": {
      "sources": ["alpha_vantage", "newsapi"],
      "priority": ["alpha_vantage", "newsapi"]
    },
    "NVDA": {
      "sources": ["alpha_vantage"],
      "priority": ["alpha_vantage"]
    }
  }
}
```

##### 新闻源可用性检查
```python
# 检查特定股票的新闻数据可用性
from src.config.news_config import news_config

# 检查AAPL的新闻数据
aapl_availability = news_config.check_ticker_availability("AAPL")
print("AAPL新闻源可用性:", aapl_availability)

# 检查所有股票的新闻数据
all_availability = news_config.check_local_data_availability()
for ticker, sources in all_availability.items():
    print(f"{ticker}: {sources}")
```

##### 新闻源优先级说明
当指定多个新闻源时，系统按以下优先级使用：

1. **Alpha Vantage** - 数据质量高，覆盖全面
2. **NewsAPI** - 更新及时，来源多样
3. **Finnhub** - 金融专业性强

**自动降级机制**：
- 如果首选新闻源数据不可用，系统会自动切换到下一个可用源
- 如果所有本地新闻源都不可用，系统会回退到API模式（需要API密钥）

### 🎯 参数组合最佳实践

#### 📈 按使用场景选择参数

##### 🚀 快速测试场景
```bash
# 适用于：功能验证、模型测试、快速原型
python src/backtester.py \
  --tickers AAPL \
  --start-date 2024-12-01 \
  --end-date 2024-12-31 \
  --model deepseek-v3 \
  --provider Groq \
  --exclude-analysts reflection_analyst
```

##### 🔬 深度研究场景
```bash
# 适用于：学术研究、策略开发、详细分析
python src/backtester.py \
  --tickers AAPL \
  --start-date 2024-01-01 \
  --end-date 2024-12-31 \
  --use-local-news \
  --news-sources alpha_vantage,newsapi,finnhub \
  --use_local_social_media \
  --track-accuracy \
  --save-reasoning \
  --save-input-data \
  --show-reasoning \
  --model gpt-4o \
  --provider OpenAI \
  --deterministic \
  --seed 42
```

##### 💰 成本控制场景
```bash
# 适用于：预算有限、大量实验、教学演示
python src/backtester.py \
  --tickers AAPL \
  --use-local-news \
  --news-sources alpha_vantage \
  --use_local_social_media \
  --model meta-llama/llama-4-scout:free \
  --provider OpenRouter \
  --analysts warren_buffett,peter_lynch
```

##### 🏭 生产环境场景
```bash
# 适用于：实际应用、稳定性要求、高质量分析
python src/backtester.py \
  --tickers AAPL,MSFT,NVDA \
  --start-date 2024-01-01 \
  --end-date 2024-12-31 \
  --use-local-news \
  --news-sources alpha_vantage,newsapi \
  --track-accuracy \
  --save-reasoning \
  --model gpt-4o \
  --provider OpenAI \
  --max-workers 4 \
  --experiment-id production_run_$(date +%Y%m%d)
```

#### 🎨 按股票特性优化参数

##### 📱 科技股优化配置
```bash
# 适用于：AAPL, MSFT, NVDA, GOOGL, TSLA
python src/backtester.py \
  --tickers NVDA \
  --use-local-news \
  --news-sources alpha_vantage \
  --use_local_social_media \
  --analysts cathie_wood,peter_lynch,fundamentals_analyst_ta \
  --model gpt-4o \
  --news-time-offset 1
```

##### 🏦 金融股优化配置
```bash
# 适用于：JPM, BAC, WFC, GS
python src/backtester.py \
  --tickers JPM \
  --use-local-news \
  --news-sources alpha_vantage,finnhub \
  --analysts warren_buffett,ben_graham,fundamentals_analyst_ta \
  --model claude-3-sonnet \
  --provider Anthropic
```

##### 🏥 医疗股优化配置
```bash
# 适用于：JNJ, PFE, UNH, ABBV
python src/backtester.py \
  --tickers JNJ \
  --use-local-news \
  --news-sources alpha_vantage,newsapi \
  --analysts peter_lynch,fundamentals_analyst_ta,news_analyst \
  --model gpt-4o \
  --news-time-offset 2
```

#### ⚡ 性能优化参数组合

##### 🏃‍♂️ 最快速度配置
```bash
# 最小化处理时间
python src/backtester.py \
  --tickers AAPL \
  --start-date 2024-12-01 \
  --end-date 2024-12-31 \
  --use-local-news \
  --news-sources alpha_vantage \
  --model deepseek-v3 \
  --provider Groq \
  --analysts warren_buffett,peter_lynch \
  --exclude-analysts reflection_analyst \
  --max-workers 1
```

##### ⚖️ 平衡配置
```bash
# 平衡速度和质量
python src/backtester.py \
  --tickers AAPL \
  --start-date 2024-06-01 \
  --end-date 2024-12-31 \
  --use-local-news \
  --news-sources alpha_vantage,newsapi \
  --model gpt-4o-mini \
  --provider OpenAI \
  --track-accuracy \
  --save-reasoning
```

##### 🎯 最高质量配置
```bash
# 最高分析质量
python src/backtester.py \
  --tickers AAPL \
  --start-date 2024-01-01 \
  --end-date 2024-12-31 \
  --use-local-news \
  --news-sources alpha_vantage,newsapi,finnhub \
  --use_local_social_media \
  --model gpt-4o \
  --provider OpenAI \
  --track-accuracy \
  --save-reasoning \
  --save-input-data \
  --show-reasoning \
  --deterministic \
  --seed 42
```

### 高级功能

#### 推理日志和准确性跟踪

**功能说明**：
- `--show-reasoning`：在终端显示AI代理的推理过程
- `--save-reasoning`：将推理过程保存到文件
- `--track-accuracy`：跟踪分析师信号的准确性
- `--save-input-data`：保存输入数据用于分析

**文件结构**：
```
reasoning_logs/
├── experiment_YYYY-MM-DD/          # 实验日期目录
│   ├── YYYY-MM-DD/                 # 交易日期目录
│   │   ├── YYYY-MM-DD_TICKER_agent_name_timestamp.json
│   │   └── ...
│   └── reflections/                # 反思分析日志
│       ├── reflection_YYYY-MM-DD.json
│       └── ...
└── accuracy_tracking/              # 准确性跟踪数据
    ├── accuracy_tracking_TICKER_DATERANGE_MODEL/
    └── ...
```

#### 确定性模式
```bash
# 确保结果可重现
python src/backtester.py \
  --tickers AAPL \
  --deterministic \
  --seed 42 \
  --experiment-id my_experiment
```

#### 选择特定分析师
```bash
# 只使用LLM智能分析师
python src/backtester.py \
  --tickers AAPL \
  --analysts fundamentals_analyst_ta,news_analyst,social_media_analyst
```

## 🔧 系统架构

### 整体架构图

```
┌─────────────────────────────────────────────────────────────────┐
│                        AI 对冲基金系统                           │
├─────────────────────────────────────────────────────────────────┤
│  📊 数据层 (Data Layer)                                         │
│  ├── 本地新闻数据 (AAPL_alpha_news/, MSFT_alpha_news/, ...)     │
│  ├── 本地社交媒体数据 (social_media_data/)                      │
│  ├── 本地金融数据 (financial_data_offline/)                     │
│  └── API数据源 (Alpha Vantage, NewsAPI, Financial Datasets)     │
├─────────────────────────────────────────────────────────────────┤
│  🤖 AI代理层 (Agent Layer)                                      │
│  ├── 投资大师代理 (10个)                                        │
│  │   ├── Warren Buffett, Charlie Munger, Ben Graham             │
│  │   ├── Peter Lynch, Phil Fisher, Cathie Wood                  │
│  │   └── Bill Ackman, Stanley Druckenmiller, Michael Burry      │
│  ├── 专业分析师代理 (7个)                                       │
│  │   ├── 基本面分析师, 技术分析师, 情绪分析师                   │
│  │   ├── 估值分析师, 事实新闻分析师, 主观新闻分析师             │
│  │   └── 反思分析师                                             │
│  └── 新一代智能分析师 (4个)                                     │
│      ├── 市场分析师 (TA), 新闻分析师 (TA)                      │
│      └── 社交媒体分析师 (TA), 基本面分析师 (TA)                │
├─────────────────────────────────────────────────────────────────┤
│  🧠 LLM层 (LLM Layer)                                          │
│  ├── OpenAI (gpt-4o, gpt-4o-mini)                              │
│  ├── Anthropic (claude-3-sonnet, claude-3-haiku)               │
│  ├── GROQ (llama-4-scout-17b, deepseek-v3)                     │
│  ├── DeepSeek (deepseek-v3)                                     │
│  ├── OpenRouter (meta-llama/llama-4-scout:free)                │
│  ├── 青云API (grok-3-reasoner, grok-beta, claude, gemini)      │
│  └── Ollama (本地模型)                                          │
├─────────────────────────────────────────────────────────────────┤
│  💼 投资组合管理层 (Portfolio Management Layer)                 │
│  ├── 投资组合管理器 - 最终决策制定                              │
│  ├── 风险管理器 - 风险评估和控制                                │
│  └── 交易执行引擎 - 买入/卖出/持有决策                          │
├─────────────────────────────────────────────────────────────────┤
│  📈 回测引擎 (Backtesting Engine)                               │
│  ├── 历史数据回测                                               │
│  ├── 性能评估和指标计算                                         │
│  ├── 准确性跟踪和分析                                           │
│  └── 推理日志和可视化                                           │
└─────────────────────────────────────────────────────────────────┘
```

### 核心工作流程

#### 1. 数据收集阶段
```
本地数据优先策略:
1. 检查本地新闻数据 (TICKER_alpha_news/)
2. 检查本地社交媒体数据 (social_media_data/TICKER_social_media/)
3. 检查本地金融数据 (financial_data_offline/TICKER_*)
4. 如果本地数据不可用，回退到API调用
5. 将API获取的数据缓存到本地
```

#### 2. AI代理分析阶段
```
多代理协作流程:
1. 数据预处理和格式化
2. 并行调用21个AI代理进行分析
3. 每个代理基于其专业领域给出投资信号
4. 收集所有代理的分析结果和推理过程
5. 反思分析师对整体决策进行质量评估
```

#### 3. 投资决策阶段
```
投资组合管理流程:
1. 投资组合管理器整合所有代理信号
2. 风险管理器评估投资风险
3. 基于风险调整后的信号做出最终决策
4. 执行买入/卖出/持有操作
5. 更新投资组合状态
```

#### 4. 性能评估阶段
```
回测和评估流程:
1. 计算投资组合性能指标
2. 跟踪代理信号准确性
3. 生成推理日志和分析报告
4. 可视化投资组合表现
5. 保存实验数据用于后续分析
```

### 关键技术特性

#### 🔄 本地数据优先策略
- **智能缓存**: 自动缓存API数据到本地
- **数据一致性**: 确保回测结果的可重现性
- **成本控制**: 减少API调用，降低使用成本
- **离线能力**: 支持完全离线回测

#### 🤖 多代理协作机制
- **专业化分工**: 每个代理专注特定投资策略
- **并行处理**: 支持多线程并行分析
- **信号聚合**: 智能整合多个代理的投资建议
- **质量控制**: 反思分析师提供决策质量评估

#### 🧠 LLM智能分析
- **多提供商支持**: 7个主要LLM提供商
- **自动降级**: API失败时自动切换提供商
- **速率限制**: 智能速率限制和重试机制
- **成本优化**: 支持免费和付费模型混合使用

#### 📊 准确性跟踪系统
- **信号验证**: 跟踪每个代理的预测准确性
- **性能分析**: 详细的准确性统计和趋势分析
- **持续改进**: 基于历史表现优化代理权重

### 🔧 参数故障排除

#### ❌ 常见参数错误

##### 错误1：日期格式不正确
```bash
# ❌ 错误格式
--start-date 01/01/2024
--start-date 2024-1-1

# ✅ 正确格式
--start-date 2024-01-01
--end-date 2024-12-31
```

##### 错误2：股票代码格式错误
```bash
# ❌ 错误格式
--tickers "AAPL MSFT NVDA"  # 空格分隔
--tickers AAPL;MSFT;NVDA    # 分号分隔

# ✅ 正确格式
--tickers AAPL,MSFT,NVDA    # 逗号分隔，无空格
```

##### 错误3：新闻源配置错误
```bash
# ❌ 错误配置
--news-sources alpha_vantage newsapi  # 缺少逗号
--news-sources "alpha_vantage,newsapi"  # 不需要引号

# ✅ 正确配置
--news-sources alpha_vantage,newsapi,finnhub
```

##### 错误4：模型和提供商不匹配
```bash
# ❌ 错误组合
--model gpt-4o --provider Groq        # GPT模型不能用Groq
--model deepseek-v3 --provider OpenAI  # DeepSeek模型不能用OpenAI

# ✅ 正确组合
--model gpt-4o --provider OpenAI
--model deepseek-v3 --provider Groq
--model claude-3-sonnet --provider Anthropic
```

#### ⚠️ 参数冲突解决

##### 冲突1：本地数据与API数据
```bash
# ⚠️ 可能的冲突
--use-local-news --news-sources alpha_vantage  # 同时指定本地和API

# ✅ 解决方案
# 方案1：只使用本地数据
--use-local-news --news-sources alpha_vantage

# 方案2：只使用API数据（需要API密钥）
--news-sources alpha_vantage
```

##### 冲突2：确定性模式与随机种子
```bash
# ⚠️ 不完整的确定性配置
--deterministic  # 缺少种子

# ✅ 完整的确定性配置
--deterministic --seed 42
```

##### 冲突3：性能与质量冲突
```bash
# ⚠️ 性能与质量冲突
--max-workers 1 --model gpt-4o --show-reasoning  # 高质量但低并发

# ✅ 平衡配置
--max-workers 4 --model gpt-4o-mini --save-reasoning
```

#### 🛠️ 参数验证工具

##### 验证命令语法
```bash
# 验证参数是否正确（不执行回测）
python src/backtester.py \
  --tickers AAPL \
  --start-date 2024-01-01 \
  --end-date 2024-12-31 \
  --validate-only
```

##### 检查数据可用性
```bash
# 检查本地新闻数据
python -c "
from src.config.news_config import news_config
print('可用股票:', list(news_config.check_local_data_availability().keys()))
print('AAPL可用源:', news_config.get_available_sources('AAPL'))
"

# 检查社交媒体数据
python -c "
from src.config.social_media_config import get_social_media_config
config = get_social_media_config()
print('社交媒体配置:', config.should_use_local_social_media())
"
```

##### 测试模型连接
```bash
# 测试OpenAI连接
python -c "
from src.utils.llm import test_llm_connection
result = test_llm_connection('gpt-4o-mini', 'OpenAI')
print('OpenAI连接状态:', result)
"

# 测试Groq连接
python -c "
from src.utils.llm import test_llm_connection
result = test_llm_connection('deepseek-v3', 'Groq')
print('Groq连接状态:', result)
"
```

### 推理日志和准确性跟踪

#### 推理日志功能
- **终端显示**：实时查看AI代理的决策过程
- **文件保存**：结构化保存所有推理数据
- **自动组织**：按实验日期和交易日期组织文件
- **格式标准**：JSON格式，便于后续分析

#### 准确性跟踪功能
- **信号准确性**：跟踪每个分析师的信号准确率
- **性能评估**：计算各种性能指标
- **趋势分析**：分析准确性随时间的变化
- **对比分析**：比较不同分析师的表现

### 📊 交互式HTML分析报告

系统提供了强大的交互式HTML分析报告功能，将代理信号分析结果以美观、直观的方式呈现。

#### 🎯 主要特性
- **🖱️ 交互式热力图**：鼠标悬停显示详细信息（代理名称、日期、信号类型、置信度）
- **🎨 现代化设计**：采用现代Web样式，美观易读
- **📈 智能颜色编码**：绿色=Bullish，红色=Bearish，灰色=Neutral，颜色深度表示置信度
- **📊 统计信息卡片**：总体统计、信号分布、最活跃代理等关键指标
- **📱 响应式布局**：适配不同屏幕尺寸

#### 🚀 使用方法
```bash
# 生成AAPL的交互式HTML分析报告
python analyze_agent_signals.py --experiment_path "reasoning_logs/experiment_AAPL_20250101-20250601_gemini2.0_flash"

# 指定输出目录
python analyze_agent_signals.py \
  --experiment_path "reasoning_logs/experiment_NVDA_20250101-20250601_gpt3.5" \
  --output_dir "./reports"
```

#### 📁 输出文件
- **HTML报告**：`agent_analysis_{TICKER}_{MODEL}_{DATE_RANGE}.html`
- **统计报告**：`agent_statistics_{TICKER}_{MODEL}_{DATE_RANGE}.txt`

#### 💡 报告内容
1. **交互式热力图**：
   - 横轴：交易日期
   - 纵轴：AI代理名称
   - 颜色：信号类型和置信度
   - 悬停：详细信息提示

2. **统计信息面板**：
   - 总体统计：总信号数、平均置信度、覆盖天数
   - 信号分布：看涨/看跌/中性信号比例
   - 最活跃代理：信号数量最多的代理及其表现

3. **图例说明**：
   - 颜色含义和使用提示
   - 交互功能说明

#### 使用示例
```bash
# 完整功能回测
python src/backtester.py \
  --tickers AAPL \
  --start-date 2024-01-01 \
  --end-date 2024-12-31 \
  --track-accuracy \
  --save-reasoning \
  --save-input-data

# 生成HTML分析报告
python analyze_agent_signals.py \
  --experiment_path "reasoning_logs/experiment_AAPL_20240101-20241231_gpt4o"
```

## 🛠️ 故障排除

### 常见问题和解决方案

#### 🔑 API密钥相关问题

##### 问题1：API密钥无效或过期
```bash
# 错误信息：Authentication failed, API key invalid
# 解决方案：
1. 检查.env文件中的API密钥是否正确
2. 确认API密钥没有过期
3. 验证API密钥格式（不要包含额外的空格或引号）

# 测试API密钥
python -c "
import os
from dotenv import load_dotenv
load_dotenv()
print('OpenAI Key:', os.getenv('OPENAI_API_KEY')[:10] + '...' if os.getenv('OPENAI_API_KEY') else 'Not found')
print('Groq Key:', os.getenv('GROQ_API_KEY1')[:10] + '...' if os.getenv('GROQ_API_KEY1') else 'Not found')
"
```

##### 问题2：速率限制错误
```bash
# 错误信息：Rate limit exceeded
# 解决方案：
1. 配置多个GROQ API密钥进行轮换
2. 使用较慢的模型或提供商
3. 增加请求间隔时间

# 配置多个GROQ密钥
GROQ_API_KEY1=your-first-key
GROQ_API_KEY2=your-second-key
GROQ_API_KEY3=your-third-key
```

#### 📊 数据相关问题

##### 问题3：本地数据不存在
```bash
# 错误信息：Local news data not found for TICKER
# 解决方案：
1. 检查数据目录是否存在
2. 确认股票代码是否支持本地数据
3. 回退到API模式或使用其他股票

# 检查数据可用性
python -c "
from src.config.news_config import news_config
availability = news_config.check_local_data_availability()
for ticker, sources in availability.items():
    print(f'{ticker}: {sources}')
"
```

##### 问题4：日期范围超出数据覆盖
```bash
# 错误信息：No data available for date range
# 解决方案：
1. 调整日期范围到支持的时间段（2024-01-01 至 2025-06-01）
2. 检查特定股票的数据覆盖范围
3. 使用API模式获取更广泛的数据

# 推荐的日期范围
--start-date 2024-01-01 --end-date 2024-12-31  # 2024年全年
--start-date 2024-06-01 --end-date 2024-12-31  # 2024年下半年
--start-date 2024-12-01 --end-date 2024-12-31  # 2024年12月
```

#### 🤖 模型相关问题

##### 问题5：模型连接失败
```bash
# 错误信息：Failed to connect to model provider
# 解决方案：
1. 检查网络连接
2. 验证API密钥
3. 尝试其他模型提供商
4. 检查提供商服务状态

# 测试模型连接
python test_api/test_qingyun_quick.py --connection
python test_api/test_qingyun_quick.py --model "gpt-4o"
```

##### 问题6：QingYun API模型错误
```bash
# 错误信息：QingYun API error or timeout
# 解决方案：
1. 检查QingYun API密钥
2. 验证网络连接到api.qingyuntop.top
3. 尝试不同的QingYun模型
4. 使用其他提供商作为备选

# QingYun API测试
python test_api/test_qingyun_comprehensive.py
python test_api/test_grok_3_reasoner.py
```

#### 💾 内存和性能问题

##### 问题7：内存不足
```bash
# 错误信息：Out of memory or system slow
# 解决方案：
1. 减少并行工作线程数
2. 缩短回测时间范围
3. 排除某些分析师
4. 使用更轻量的模型

# 内存优化配置
python src/backtester.py \
  --tickers AAPL \
  --max-workers 1 \
  --exclude-analysts reflection_analyst \
  --model deepseek-v3 \
  --provider Groq
```

##### 问题8：回测速度过慢
```bash
# 解决方案：
1. 使用本地数据而非API调用
2. 选择更快的模型（如deepseek-v3）
3. 减少分析师数量
4. 增加并行工作线程数

# 速度优化配置
python src/backtester.py \
  --tickers AAPL \
  --use-local-news \
  --use_local_social_media \
  --model deepseek-v3 \
  --provider Groq \
  --exclude-analysts reflection_analyst \
  --max-workers 4
```

## ❓ 常见问题

### 📈 回测相关问题

**Q1: 为什么我的回测结果每次都不一样？**
A: 默认情况下，系统使用随机种子。要获得可重现的结果，请使用确定性模式：
```bash
python src/backtester.py --tickers AAPL --deterministic --seed 42
```

**Q2: 哪些股票支持完整的本地数据？**
A: 目前完全支持本地数据的股票包括：
- AAPL, MSFT, NVDA（新闻+社交媒体+金融数据）
- GOOGL, TSLA（新闻+社交媒体，金融数据需API）

**Q3: 如何选择最适合的LLM模型？**
A: 根据需求选择：
- 高质量分析：gpt-4o, claude-3-sonnet
- 快速回测：deepseek-v3, llama-4-scout-17b
- 成本控制：gpt-4o-mini, meta-llama/llama-4-scout:free
- 离线使用：Ollama本地模型

### 🔧 技术问题

**Q4: 如何处理"反思分析师"错误？**
A: 反思分析师在处理大量数据时可能出错，解决方案：
```bash
# 方案1：排除反思分析师
python src/backtester.py --tickers AAPL --exclude-analysts reflection_analyst

# 方案2：使用更强大的模型（如grok-3-reasoner）
python src/backtester.py --tickers AAPL --model grok-3-reasoner --provider QingYun
```

**Q5: 为什么QingYun模型比其他模型慢？**
A: QingYun模型自动应用30秒延迟以防止服务器饱和，这是正常的保护机制。

**Q6: 如何减少API调用成本？**
A: 使用本地数据优先策略：
```bash
python src/backtester.py \
  --tickers AAPL \
  --use-local-news \
  --use_local_social_media \
  --model meta-llama/llama-4-scout:free \
  --provider OpenRouter
```

### 📊 数据问题

**Q7: 本地新闻数据的时间偏移是什么意思？**
A: 时间偏移用于模拟真实交易中的信息延迟：
- `--news-time-offset 0`：使用当日新闻（理想情况）
- `--news-time-offset 1`：使用前一日新闻（更现实）

**Q8: 如何添加新的股票支持？**
A: 需要准备相应的本地数据：
1. 新闻数据：创建`TICKER_alpha_news/`目录
2. 社交媒体数据：创建`social_media_data/TICKER_social_media/`目录
3. 金融数据：创建`financial_data_offline/TICKER_*/`目录

### 🚀 性能优化

**Q9: 如何提高回测速度？**
A: 多种优化策略：
```bash
# 最快配置
python src/backtester.py \
  --tickers AAPL \
  --start-date 2024-12-01 \
  --end-date 2024-12-31 \
  --use-local-news \
  --model deepseek-v3 \
  --provider Groq \
  --exclude-analysts reflection_analyst \
  --max-workers 1
```

**Q10: 推理日志文件太大怎么办？**
A: 可以选择性保存日志：
```bash
# 只保存推理，不保存输入数据
python src/backtester.py --tickers AAPL --save-reasoning

# 只在终端显示，不保存文件
python src/backtester.py --tickers AAPL --show-reasoning
```

### 📋 参数快速参考

#### 🚀 常用参数组合速查表

| 使用场景 | 推荐参数组合 | 说明 |
|----------|-------------|------|
| **快速测试** | `--tickers AAPL --start-date 2024-12-01 --end-date 2024-12-31` | 最小配置，快速验证 |
| **本地数据** | `--use-local-news --news-sources alpha_vantage --use_local_social_media` | 使用本地数据，避免API调用 |
| **高质量分析** | `--model gpt-4o --provider OpenAI --track-accuracy --save-reasoning` | 最佳分析质量 |
| **成本控制** | `--model meta-llama/llama-4-scout:free --provider OpenRouter` | 免费模型 |
| **调试模式** | `--show-reasoning --save-reasoning --save-input-data` | 详细日志输出 |
| **生产环境** | `--deterministic --seed 42 --experiment-id prod_run` | 可重现结果 |

#### 🔧 环境变量配置

除了命令行参数，您还可以通过环境变量配置默认值：

```bash
# .env 文件配置示例

# === 基本配置 ===
DEFAULT_TICKERS=AAPL,MSFT,NVDA
DEFAULT_START_DATE=2024-01-01
DEFAULT_END_DATE=2024-12-31
INITIAL_CAPITAL=100000

# === 新闻数据配置 ===
NEWS_USE_LOCAL_DATA=true
NEWS_SOURCES=alpha_vantage,newsapi,finnhub
NEWS_TIME_OFFSET_DAYS=1

# === 社交媒体配置 ===
SOCIAL_MEDIA_USE_LOCAL=true
SOCIAL_MEDIA_SOURCES=reddit
SOCIAL_MEDIA_TIME_OFFSET_HOURS=2

# === LLM配置 ===
DEFAULT_MODEL=gpt-4o-mini
DEFAULT_PROVIDER=OpenAI
ENABLE_REASONING_LOGS=true

# === 性能配置 ===
MAX_WORKERS=4
ENABLE_CACHING=true
CACHE_EXPIRY_HOURS=24

# === 调试配置 ===
LOG_LEVEL=INFO
SAVE_REASONING=false
TRACK_ACCURACY=false
SHOW_REASONING=false
```

#### 📊 参数优先级

参数的优先级顺序（从高到低）：

1. **命令行参数** - 最高优先级
2. **环境变量** - 中等优先级
3. **配置文件** - 较低优先级
4. **默认值** - 最低优先级

```bash
# 示例：命令行参数会覆盖环境变量
export DEFAULT_MODEL=gpt-4o-mini
python src/backtester.py --model gpt-4o  # 实际使用 gpt-4o
```

#### 🎯 参数验证规则

| 参数类型 | 验证规则 | 示例 |
|----------|----------|------|
| **日期** | YYYY-MM-DD格式 | `2024-01-01` |
| **股票代码** | 大写字母，逗号分隔 | `AAPL,MSFT,NVDA` |
| **数字** | 正整数或浮点数 | `100000`, `42`, `1.5` |
| **布尔值** | 存在即为True | `--track-accuracy` |
| **枚举值** | 预定义选项 | `--provider OpenAI` |

### 性能优化建议

#### 提高回测速度
1. **使用本地新闻数据**：避免API调用延迟
2. **选择快速模型**：如deepseek-v3, llama-4-scout-17b
3. **减少分析师数量**：只选择必要的分析师
4. **缩短时间范围**：先用短期数据测试
5. **优化新闻源选择**：
   ```bash
   # 单源模式（最快）
   --news-sources alpha_vantage

   # 双源模式（平衡）
   --news-sources alpha_vantage,newsapi

   # 避免使用所有源（较慢）
   --news-sources alpha_vantage,newsapi,finnhub
   ```

#### 提高分析质量
1. **使用高质量模型**：如gpt-4o, claude-3-sonnet
2. **启用推理日志**：便于调试和优化
3. **跟踪准确性**：监控分析师表现
4. **优化新闻源组合**：
   ```bash
   # 高质量分析配置
   # AAPL: 使用所有源获得最全面的信息
   python src/backtester.py \
     --tickers AAPL \
     --news-sources alpha_vantage,newsapi,finnhub \
     --model gpt-4o

   # MSFT: 使用双源平衡质量和速度
   python src/backtester.py \
     --tickers MSFT \
     --news-sources alpha_vantage,newsapi \
     --model claude-3-sonnet

   # NVDA: 使用单源但高质量模型
   python src/backtester.py \
     --tickers NVDA \
     --news-sources alpha_vantage \
     --model gpt-4o
   ```

#### 新闻源选择最佳实践

##### 按用途选择新闻源
- **快速测试**：只使用 `alpha_vantage`
- **平衡分析**：使用 `alpha_vantage,newsapi`
- **深度分析**：使用所有可用源
- **成本控制**：优先使用本地数据，避免API调用

##### 按股票特性选择
- **大盘股（AAPL, MSFT）**：使用多源获得全面信息
- **科技股（NVDA）**：重点关注Alpha Vantage的专业分析
- **新兴股票**：优先使用NewsAPI获得最新资讯

##### 新闻源组合建议
```bash
# 推荐组合1：质量优先
AAPL: alpha_vantage,newsapi,finnhub
MSFT: alpha_vantage,newsapi
NVDA: alpha_vantage

# 推荐组合2：速度优先
所有股票: alpha_vantage

# 推荐组合3：平衡模式
AAPL,MSFT: alpha_vantage,newsapi
NVDA: alpha_vantage
```




## 📁 项目结构

```
ai-hedge-fund/
├── 📂 src/                          # 核心源代码
│   ├── 📂 agents/                   # AI代理实现
│   │   ├── 🤖 warren_buffett.py     # 巴菲特价值投资代理
│   │   ├── 🤖 charlie_munger.py     # 芒格多元思维代理
│   │   ├── 🤖 ben_graham.py         # 格雷厄姆价值投资代理
│   │   ├── 🤖 phil_fisher.py        # 费雪成长投资代理
│   │   ├── 🤖 peter_lynch.py        # 林奇实用投资代理
│   │   ├── 🤖 cathie_wood.py        # 伍德创新投资代理
│   │   ├── 🤖 bill_ackman.py        # 阿克曼激进投资代理
│   │   ├── 🤖 michael_burry.py      # 伯里逆势投资代理
│   │   ├── 🤖 aswath_damodaran.py   # 达摩达兰估值代理
│   │   ├── 🤖 stanley_druckenmiller.py # 德鲁肯米勒宏观代理
│   │   ├── 📊 fundamentals_analyst.py # 基本面分析师（支持LLM）
│   │   ├── 📊 market_analyst.py     # 市场分析师（支持LLM）
│   │   ├── 📊 news_analyst.py       # 新闻分析师（支持LLM）
│   │   ├── 📊 social_media_analyst.py # 社交媒体分析师（支持LLM）
│   │   ├── 📊 technicals.py         # 技术分析代理
│   │   ├── 📊 sentiment.py          # 情绪分析代理
│   │   ├── 📊 valuation.py          # 估值分析代理
│   │   ├── 🛡️ risk_manager.py       # 风险管理代理
│   │   ├── 💼 portfolio_manager.py  # 投资组合管理代理
│   │   └── 🔄 reflection_analyst.py # 反思分析代理
│   ├── 📂 config/                   # 配置管理
│   │   └── 📰 news_config.py        # 新闻数据配置
│   ├── 📂 data/                     # 数据处理
│   │   ├── 💾 cache.py              # 本地缓存系统
│   │   └── 📋 models.py             # 数据模型定义
│   ├── 📂 tools/                    # 工具模块
│   │   ├── 🔌 api.py                # API集成工具
│   │   └── 📰 local_news_reader.py  # 本地新闻读取器
│   ├── 📂 llm/                      # LLM集成
│   │   └── 🧠 models.py             # LLM模型定义
│   ├── 📂 utils/                    # 实用工具
│   │   ├── 👥 analysts.py           # 分析师配置
│   │   ├── 🎨 display.py            # 结果显示
│   │   └── 📊 visualize.py          # 数据可视化
│   ├── 🚀 main.py                   # 主程序入口
│   └── 📈 backtester.py             # 回测系统
├── 📂 app/                          # Web应用
│   ├── 📂 backend/                  # 后端API
│   │   ├── 📂 routes/               # API路由
│   │   ├── 📂 services/             # 业务服务
│   │   └── 🌐 main.py               # FastAPI应用
│   └── 📂 frontend/                 # 前端界面
│       ├── 📂 src/                  # React源代码
│       │   ├── 📂 components/       # React组件
│       │   └── 📂 services/         # 前端服务
│       └── 📦 package.json          # 前端依赖
├── 📂 docs/                         # 文档目录
│   ├── 📖 backtester_usage_guide.md # 回测使用指南
│   ├── 📖 groq_api_rotation_guide.md # GROQ API轮换指南
│   └── 📖 openrouter_integration_guide.md # OpenRouter集成指南
├── 📂 data/                         # 数据目录
│   ├── 📂 AAPL_alpha_news/          # AAPL Alpha Vantage新闻
│   ├── 📂 MSFT_alpha_news/          # MSFT Alpha Vantage新闻
│   ├── 📂 NVDA_alpha_news/          # NVDA Alpha Vantage新闻
│   └── 📂 social_media_data/        # 社交媒体数据
├── 📂 reasoning_logs/               # 推理日志
│   ├── 📂 experiment_YYYY-MM-DD/    # 实验日志
│   ├── 📂 accuracy_tracking/        # 准确性跟踪
│   └── 📂 reflections/              # 反思分析日志
├── 🐳 Dockerfile                    # Docker构建文件
├── 🐳 docker-compose.yml            # Docker编排文件
├── 📋 pyproject.toml                # Poetry项目配置
├── 🔧 .env.example                  # 环境变量示例
├── 📊 stocktwits_data_collector.py  # StockTwits数据收集器
└── 📖 README.md                     # 项目说明文档
```

### 核心文件说明

#### 🚀 主要入口点
- **`src/main.py`**: AI对冲基金主程序，处理命令行参数，创建代理工作流
- **`src/backtester.py`**: 回测系统入口，模拟历史交易策略表现
- **`app/backend/main.py`**: Web API后端服务
- **`app/frontend/src/App.tsx`**: React前端应用主组件

#### 🤖 代理系统
- **投资大师代理**: 模拟10位知名投资者的投资策略和决策风格
- **专业分析师**: 7个专业化分析代理，支持传统算法和LLM智能分析
- **管理代理**: 风险管理、投资组合管理、反思分析等核心功能

#### 📊 数据处理
- **`src/tools/api.py`**: 金融数据API集成，支持多种数据源
- **`src/tools/local_news_reader.py`**: 本地新闻数据读取器
- **`src/data/cache.py`**: 本地缓存系统，减少API调用
- **`src/config/news_config.py`**: 新闻数据配置管理

#### 🧠 LLM集成
- **`src/llm/models.py`**: LLM模型定义和工具
- **`src/utils/llm.py`**: LLM调用工具和提示工程
- **多提供商支持**: OpenAI、Groq、Anthropic、DeepSeek等

#### 📈 回测和分析
- **推理日志系统**: 详细记录AI代理的决策过程
- **准确性跟踪**: 监控分析师信号的准确性
- **反思学习**: 持续改进决策质量的学习机制
- **性能评估**: 全面的回测性能分析和可视化
- **交互式HTML报告**: 生成美观的交互式代理信号分析报告





## 🤝 贡献指南

我们欢迎社区贡献！无论是bug修复、新功能开发还是文档改进，都非常感谢您的参与。

### 🚀 如何贡献

#### 1. 准备开发环境

```bash
# Fork并克隆仓库
git clone https://github.com/your-username/ai-hedge-fund.git
cd ai-hedge-fund

# 安装开发依赖
poetry install --with dev

# 安装pre-commit钩子
pre-commit install
```

#### 2. 创建功能分支

```bash
# 从main分支创建新分支
git checkout -b feature/your-feature-name

# 或者修复bug
git checkout -b fix/bug-description
```

#### 3. 开发和测试

```bash
# 运行测试
poetry run pytest

# 代码格式化
poetry run black src/
poetry run isort src/

# 类型检查
poetry run mypy src/
```

#### 4. 提交更改

```bash
# 添加更改
git add .

# 提交（遵循约定式提交格式）
git commit -m "feat: add new investment strategy agent"
git commit -m "fix: resolve API rate limit issue"
git commit -m "docs: update installation guide"
```

#### 5. 创建Pull Request

- 推送分支到您的fork
- 在GitHub上创建Pull Request
- 填写PR模板，描述您的更改
- 等待代码审查和反馈

### 📋 贡献类型

#### 🐛 Bug修复
- 修复现有功能的问题
- 改进错误处理
- 性能优化

#### ✨ 新功能
- 新的投资策略代理
- 新的数据源集成
- 新的分析工具

#### 📚 文档改进
- API文档
- 使用指南
- 代码注释

#### 🧪 测试
- 单元测试
- 集成测试
- 性能测试

### 🎯 开发重点领域

#### 1. 新投资策略代理
我们欢迎添加新的投资大师代理，例如：
- **Ray Dalio** - 桥水基金创始人，全天候投资策略
- **Howard Marks** - 橡树资本联合创始人，逆向投资专家
- **Joel Greenblatt** - 神奇公式投资法创始人
- **David Einhorn** - 绿光资本创始人，价值投资者

#### 2. 数据源扩展
- 更多新闻数据源（Bloomberg, Reuters等）
- 另类数据（卫星数据、信用卡交易等）
- 国际市场数据支持
- 加密货币数据集成

#### 3. 分析工具增强
- 更多技术指标
- 宏观经济指标分析
- ESG（环境、社会、治理）评分
- 行业轮动分析

#### 4. 用户体验改进
- 更好的可视化界面
- 实时数据流支持
- 移动端适配
- 多语言支持

### 📝 代码规范

#### Python代码风格
```python
# 使用类型提示
def calculate_sharpe_ratio(returns: List[float], risk_free_rate: float = 0.02) -> float:
    """计算夏普比率

    Args:
        returns: 收益率列表
        risk_free_rate: 无风险利率

    Returns:
        夏普比率
    """
    pass

# 使用Pydantic模型
class InvestmentSignal(BaseModel):
    signal: Literal["bullish", "bearish", "neutral"]
    confidence: float = Field(ge=0, le=100)
    reasoning: str
    timestamp: datetime = Field(default_factory=datetime.now)
```

#### 提交信息格式
遵循[约定式提交](https://www.conventionalcommits.org/zh-hans/)格式：

```
<类型>[可选的作用域]: <描述>

[可选的正文]

[可选的脚注]
```

**类型**：
- `feat`: 新功能
- `fix`: bug修复
- `docs`: 文档更新
- `style`: 代码格式（不影响功能）
- `refactor`: 重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

**示例**：
```
feat(agents): add Ray Dalio investment strategy agent

- Implement All Weather portfolio strategy
- Add risk parity calculation
- Include macroeconomic analysis

Closes #123
```

### 🧪 测试指南

#### 单元测试
```python
import pytest
from src.agents.warren_buffett import WarrenBuffettAgent

def test_warren_buffett_signal_generation():
    """测试巴菲特代理信号生成"""
    agent = WarrenBuffettAgent()

    # 模拟输入数据
    mock_data = {
        "ticker": "AAPL",
        "financial_metrics": {...},
        "news_data": [...]
    }

    # 测试信号生成
    signal = agent.generate_signal(mock_data)

    assert signal.signal in ["bullish", "bearish", "neutral"]
    assert 0 <= signal.confidence <= 100
    assert len(signal.reasoning) > 0
```

#### 集成测试
```python
def test_full_backtest_pipeline():
    """测试完整回测流程"""
    from src.backtester import Backtester
    from src.main import run_hedge_fund

    backtester = Backtester(
        agent=run_hedge_fund,
        tickers=["AAPL"],
        start_date="2024-01-01",
        end_date="2024-01-05",
        initial_capital=100000
    )

    performance = backtester.run_backtest()

    assert "sharpe_ratio" in performance
    assert "max_drawdown" in performance
```

### 🏆 贡献者认可

我们重视每一位贡献者的努力：

- **代码贡献者**：在README中列出
- **文档贡献者**：在文档页面中致谢
- **Bug报告者**：在发布说明中感谢
- **功能建议者**：在功能实现时致谢

### 📞 获取帮助

如果您在贡献过程中遇到问题：

1. **查看文档**：检查现有文档和示例
2. **搜索Issues**：查看是否有类似问题
3. **创建Issue**：描述您的问题或建议
4. **加入讨论**：参与GitHub Discussions

### 🎉 感谢贡献者

感谢所有为这个项目做出贡献的开发者、测试者、文档编写者和用户！

---

**记住**：每个贡献都很重要，无论大小。我们期待您的参与！

## 📄 许可证

本项目采用 **MIT 许可证** - 详情请参阅 [LICENSE](LICENSE) 文件。

### MIT 许可证摘要

✅ **允许的使用**：
- 商业使用
- 修改
- 分发
- 私人使用

⚠️ **条件**：
- 包含许可证和版权声明

❌ **限制**：
- 不提供责任保证
- 不提供保修

### 第三方许可证

本项目使用了以下开源库，请遵守相应的许可证：

- **LangChain** - MIT License
- **FastAPI** - MIT License
- **React** - MIT License
- **OpenAI Python** - Apache 2.0 License
- **Anthropic SDK** - MIT License
- **Pydantic** - MIT License

---

## 🌟 致谢

### 核心贡献者
- **[@virattt](https://github.com/virattt)** - 项目创始人和主要维护者

### 特别感谢
- **投资大师们** - Warren Buffett, Charlie Munger, Ben Graham等，他们的投资智慧启发了这个项目
- **开源社区** - 感谢所有开源库的维护者和贡献者
- **AI/ML社区** - 感谢推动人工智能发展的研究者和开发者

### 数据提供商
- **Financial Datasets** - 金融数据API
- **Alpha Vantage** - 新闻和市场数据
- **NewsAPI** - 新闻数据服务
- **Finnhub** - 实时金融数据
- **StockTwits** - 社交媒体数据

---

## 📞 联系我们

### 项目相关
- **GitHub Issues**: [报告问题或建议功能](https://github.com/virattt/ai-hedge-fund/issues)
- **GitHub Discussions**: [参与社区讨论](https://github.com/virattt/ai-hedge-fund/discussions)
- **Twitter**: [@virattt](https://twitter.com/virattt)

### 商业合作
如果您对商业合作、企业级支持或定制开发感兴趣，请通过以下方式联系：
- 创建GitHub Issue并标记为 `business-inquiry`
- 通过Twitter私信联系

---

## 🎯 项目愿景

我们的愿景是创建一个开放、透明、教育性的AI投资研究平台，让每个人都能：

1. **学习投资智慧** - 通过模拟大师级投资者的思维过程
2. **理解AI应用** - 探索人工智能在金融领域的实际应用
3. **提升投资素养** - 通过实践和分析提高投资决策能力
4. **推动创新** - 为AI+金融领域的发展贡献力量

### 未来发展方向

#### 短期目标（3-6个月）
- 🎯 添加更多投资大师代理（Ray Dalio, Howard Marks等）
- 📊 增强数据可视化和报告功能
- 🌍 支持国际市场和多币种
- 📱 开发移动端应用

#### 中期目标（6-12个月）
- 🤖 集成更多AI模型和技术
- 📈 实时交易信号和警报系统
- 🔗 区块链和DeFi集成
- 🎓 在线教育和培训模块

#### 长期目标（1-2年）
- 🏢 企业级解决方案
- 🌐 全球社区和生态系统
- 🔬 学术研究合作
- 🚀 商业化产品和服务

---

## 🔮 结语

AI对冲基金项目代表了人工智能与金融投资结合的一次有意义的探索。我们相信，通过开源协作和社区驱动的方式，可以创造出真正有价值的教育和研究工具。

**记住**：投资有风险，决策需谨慎。本项目仅供学习和研究使用，不构成投资建议。

感谢您对AI对冲基金项目的关注和支持！让我们一起探索AI与投资的无限可能。

---

<div align="center">

**⭐ 如果这个项目对您有帮助，请给我们一个Star！ ⭐**

[![GitHub stars](https://img.shields.io/github/stars/virattt/ai-hedge-fund?style=social)](https://github.com/virattt/ai-hedge-fund/stargazers)
[![GitHub forks](https://img.shields.io/github/forks/virattt/ai-hedge-fund?style=social)](https://github.com/virattt/ai-hedge-fund/network/members)
[![Twitter Follow](https://img.shields.io/twitter/follow/virattt?style=social)](https://twitter.com/virattt)

**让AI投资智慧触手可及 🚀**

</div>
