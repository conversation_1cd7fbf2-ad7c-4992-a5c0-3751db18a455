#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GOOGL和TSLA数据设置演示脚本

该脚本演示如何为GOOGL和TSLA股票设置完整的数据支持，
包括测试环境、下载数据和运行回测的完整流程。

使用方法:
    python demo_googl_tsla_setup.py
    python demo_googl_tsla_setup.py --demo-mode  # 演示模式，不实际下载
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path
from datetime import datetime

def print_header(title: str):
    """打印标题"""
    print("\n" + "=" * 60)
    print(f"🎯 {title}")
    print("=" * 60)

def print_step(step: str, description: str):
    """打印步骤"""
    print(f"\n📋 步骤 {step}: {description}")
    print("-" * 40)

def run_command(command: str, description: str, demo_mode: bool = False) -> bool:
    """运行命令"""
    print(f"🔄 {description}")
    print(f"💻 命令: {command}")
    
    if demo_mode:
        print("🎭 演示模式：跳过实际执行")
        return True
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 执行成功")
            if result.stdout.strip():
                print(f"📤 输出: {result.stdout.strip()[:200]}...")
            return True
        else:
            print("❌ 执行失败")
            if result.stderr.strip():
                print(f"📥 错误: {result.stderr.strip()[:200]}...")
            return False
    except Exception as e:
        print(f"❌ 执行异常: {e}")
        return False

def check_prerequisites():
    """检查先决条件"""
    print_step("1", "检查先决条件")
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"🐍 Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 8):
        print("❌ Python版本过低，需要3.8或更高版本")
        return False
    
    # 检查项目结构
    required_dirs = ['src', 'src/tools', 'src/agents']
    required_files = ['src/backtester.py', 'src/tools/api.py']
    
    for dir_path in required_dirs:
        if not Path(dir_path).exists():
            print(f"❌ 缺少目录: {dir_path}")
            return False
        else:
            print(f"✅ 目录存在: {dir_path}")
    
    for file_path in required_files:
        if not Path(file_path).exists():
            print(f"❌ 缺少文件: {file_path}")
            return False
        else:
            print(f"✅ 文件存在: {file_path}")
    
    # 检查环境变量
    env_vars = ['FINANCIAL_DATASETS_API_KEY']
    for var in env_vars:
        if os.getenv(var):
            print(f"✅ 环境变量已设置: {var}")
        else:
            print(f"⚠️ 环境变量未设置: {var}")
    
    return True

def check_existing_data():
    """检查现有数据"""
    print_step("2", "检查现有数据")
    
    data_status = {
        'GOOGL': {'news': False, 'social': False, 'financial': False},
        'TSLA': {'news': False, 'social': False, 'financial': False}
    }
    
    # 检查新闻数据
    for ticker in ['GOOGL', 'TSLA']:
        news_dir = Path(f"{ticker}_alpha_news")
        if news_dir.exists() and list(news_dir.glob("*.json")):
            data_status[ticker]['news'] = True
            print(f"✅ {ticker} 新闻数据: 已存在")
        else:
            print(f"❌ {ticker} 新闻数据: 不存在")
    
    # 检查社交媒体数据
    for ticker in ['GOOGL', 'TSLA']:
        social_dir = Path(f"social_media_data/{ticker}_social_media")
        if social_dir.exists() and list(social_dir.glob("*.json")):
            data_status[ticker]['social'] = True
            print(f"✅ {ticker} 社交媒体数据: 已存在")
        else:
            print(f"❌ {ticker} 社交媒体数据: 不存在")
    
    # 检查金融数据
    financial_base = Path("financial_data_offline")
    if financial_base.exists():
        for ticker in ['GOOGL', 'TSLA']:
            has_financial = False
            data_types = ['prices', 'line_items', 'market_cap']
            
            for data_type in data_types:
                data_dir = financial_base / f"{ticker}_{data_type}"
                if data_dir.exists() and list(data_dir.glob("*.json")):
                    has_financial = True
                    break
            
            if has_financial:
                data_status[ticker]['financial'] = True
                print(f"✅ {ticker} 金融数据: 已存在")
            else:
                print(f"❌ {ticker} 金融数据: 不存在")
    else:
        print("❌ 金融数据目录不存在")
    
    return data_status

def run_test_script(demo_mode: bool = False):
    """运行测试脚本"""
    print_step("3", "运行环境测试")
    
    if not Path("test_googl_tsla_download.py").exists():
        print("❌ 测试脚本不存在")
        return False
    
    return run_command(
        "python test_googl_tsla_download.py",
        "运行环境测试脚本",
        demo_mode
    )

def download_financial_data(demo_mode: bool = False):
    """下载金融数据"""
    print_step("4", "下载金融数据")
    
    if not Path("download_googl_tsla_financial_data.py").exists():
        print("❌ 下载脚本不存在")
        return False
    
    # 先下载少量数据进行测试
    test_command = "python download_googl_tsla_financial_data.py --data-types prices --start-date 2024-01-01 --end-date 2024-01-05"
    
    if run_command(test_command, "下载测试数据（价格数据，5天）", demo_mode):
        print("✅ 测试下载成功")
        
        # 询问是否下载完整数据
        if not demo_mode:
            response = input("\n🤔 是否下载完整数据？这可能需要几分钟时间 (y/N): ")
            if response.lower() in ['y', 'yes']:
                full_command = "python download_googl_tsla_financial_data.py"
                return run_command(full_command, "下载完整金融数据", demo_mode)
            else:
                print("⏭️ 跳过完整数据下载")
                return True
        else:
            print("🎭 演示模式：跳过完整数据下载")
            return True
    else:
        print("❌ 测试下载失败")
        return False

def run_sample_backtest(demo_mode: bool = False):
    """运行示例回测"""
    print_step("5", "运行示例回测")
    
    # 简单的回测命令
    backtest_commands = [
        {
            'command': 'python src/backtester.py --tickers GOOGL --start-date 2024-01-01 --end-date 2024-01-05 --use-local-news',
            'description': 'GOOGL短期回测（5天）'
        },
        {
            'command': 'python src/backtester.py --tickers TSLA --start-date 2024-01-01 --end-date 2024-01-05 --use-local-news',
            'description': 'TSLA短期回测（5天）'
        }
    ]
    
    success_count = 0
    for cmd_info in backtest_commands:
        if run_command(cmd_info['command'], cmd_info['description'], demo_mode):
            success_count += 1
    
    return success_count > 0

def generate_summary(data_status: dict, test_success: bool, download_success: bool, backtest_success: bool):
    """生成总结报告"""
    print_header("设置总结报告")
    
    print("📊 数据状态:")
    for ticker in ['GOOGL', 'TSLA']:
        print(f"\n  {ticker}:")
        status = data_status[ticker]
        print(f"    新闻数据: {'✅' if status['news'] else '❌'}")
        print(f"    社交媒体: {'✅' if status['social'] else '❌'}")
        print(f"    金融数据: {'✅' if status['financial'] else '❌'}")
    
    print(f"\n🧪 测试结果: {'✅ 通过' if test_success else '❌ 失败'}")
    print(f"📥 数据下载: {'✅ 成功' if download_success else '❌ 失败'}")
    print(f"🔄 回测运行: {'✅ 成功' if backtest_success else '❌ 失败'}")
    
    # 生成建议
    print("\n💡 建议:")
    
    if not test_success:
        print("  1. 检查环境配置和API密钥")
        print("  2. 确保所有依赖已正确安装")
    
    if not download_success:
        print("  3. 检查网络连接和API限制")
        print("  4. 验证FINANCIAL_DATASETS_API_KEY是否有效")
    
    if not backtest_success:
        print("  5. 确保有足够的数据进行回测")
        print("  6. 检查回测参数设置")
    
    if test_success and download_success and backtest_success:
        print("  🎉 所有设置完成！可以开始使用GOOGL和TSLA进行回测")
        print("\n📖 下一步:")
        print("  - 运行完整的回测: python src/backtester.py --tickers GOOGL,TSLA")
        print("  - 查看分析结果: python analyze_agent_signals.py")
        print("  - 参考详细指南: docs/googl_tsla_setup_guide.md")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='GOOGL和TSLA数据设置演示')
    parser.add_argument('--demo-mode', action='store_true',
                       help='演示模式，不实际执行命令')
    
    args = parser.parse_args()
    
    print_header("GOOGL和TSLA数据设置演示")
    
    if args.demo_mode:
        print("🎭 运行在演示模式，不会实际执行命令")
    
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 执行设置步骤
    prereq_success = check_prerequisites()
    data_status = check_existing_data()
    test_success = run_test_script(args.demo_mode) if prereq_success else False
    download_success = download_financial_data(args.demo_mode) if test_success else False
    backtest_success = run_sample_backtest(args.demo_mode) if download_success else False
    
    # 生成总结
    generate_summary(data_status, test_success, download_success, backtest_success)
    
    print(f"\n⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
