# GOOGL和TSLA股票数据设置指南

本指南将帮助您为GOOGL（Alphabet Inc.）和TSLA（Tesla Inc.）股票设置完整的数据支持，包括新闻数据、社交媒体数据和金融数据。

## 📋 数据支持现状

### GOOGL (Alphabet Inc.)
- ✅ **新闻数据**: 已有本地Alpha Vantage数据 (`GOOGL_alpha_news/`)
- ✅ **社交媒体数据**: 已有本地Reddit数据 (`social_media_data/GOOGL_social_media/`)
- 🔄 **金融数据**: 需要下载到本地缓存

### TSLA (Tesla Inc.)
- ✅ **新闻数据**: 已有本地Alpha Vantage数据 (`TSLA_alpha_news/`)
- ✅ **社交媒体数据**: 已有本地Reddit数据 (`social_media_data/TSLA_social_media/`)
- 🔄 **金融数据**: 需要下载到本地缓存

## 🚀 快速开始

### 1. 环境检查
首先运行测试脚本检查环境配置：

```bash
python test_googl_tsla_download.py
```

这个脚本会检查：
- API模块导入是否正常
- 环境变量是否设置正确
- 数据目录是否可以创建
- JSON文件操作是否正常
- 单个API调用是否成功

### 2. 下载金融数据
运行下载脚本获取金融数据：

```bash
# 下载所有数据类型（推荐）
python download_googl_tsla_financial_data.py

# 只下载特定股票
python download_googl_tsla_financial_data.py --tickers GOOGL
python download_googl_tsla_financial_data.py --tickers TSLA

# 自定义时间范围
python download_googl_tsla_financial_data.py --start-date 2024-01-01 --end-date 2025-06-01

# 只下载特定数据类型
python download_googl_tsla_financial_data.py --data-types prices line_items market_cap
```

### 3. 验证数据完整性
下载完成后，检查数据目录结构：

```
financial_data_offline/
├── GOOGL_line_items/
├── GOOGL_market_cap/
├── GOOGL_prices/
├── GOOGL_insider_trades/
├── GOOGL_company_news/
├── GOOGL_financial_metrics/
├── TSLA_line_items/
├── TSLA_market_cap/
├── TSLA_prices/
├── TSLA_insider_trades/
├── TSLA_company_news/
└── TSLA_financial_metrics/
```

### 4. 运行回测
数据准备完成后，可以运行回测：

```bash
# GOOGL回测
python src/backtester.py --tickers GOOGL --use-local-news --use-local-social-media --start-date 2024-01-01 --end-date 2024-12-31

# TSLA回测
python src/backtester.py --tickers TSLA --use-local-news --use-local-social-media --start-date 2024-01-01 --end-date 2024-12-31

# 同时回测两个股票
python src/backtester.py --tickers GOOGL,TSLA --use-local-news --use-local-social-media --start-date 2024-01-01 --end-date 2024-12-31
```

## 📊 数据类型说明

### 金融数据类型
1. **line_items**: 财务报表数据（季度更新）
   - 收入、净利润、营业收入等关键财务指标
   - 用于基本面分析

2. **market_cap**: 市值数据（每日更新）
   - 市场资本化数据
   - 用于估值分析

3. **prices**: 股价数据（每日更新）
   - 开盘价、收盘价、最高价、最低价、成交量
   - 用于技术分析

4. **insider_trades**: 内部交易数据
   - 公司内部人员的买卖交易记录
   - 用于情绪分析

5. **company_news**: 公司新闻数据
   - 来自Financial Datasets API的新闻
   - 补充Alpha Vantage新闻数据

6. **financial_metrics**: 财务指标数据
   - PE比率、PB比率等估值指标
   - 用于估值分析

## ⚙️ 配置说明

### 环境变量
确保在`.env`文件中设置以下变量：

```bash
# 必需的API密钥
FINANCIAL_DATASETS_API_KEY=your-financial-datasets-api-key

# 可选的API密钥（用于补充数据）
ALPHA_VANTAGE_API_KEY=your-alpha-vantage-key
NEWSAPI_KEY=your-newsapi-key
FINNHUB_API_KEY=your-finnhub-key
```

### 下载脚本参数
- `--tickers`: 指定要下载的股票代码（默认：GOOGL TSLA）
- `--start-date`: 开始日期（默认：2024-01-01）
- `--end-date`: 结束日期（默认：2025-06-01）
- `--data-types`: 指定数据类型（默认：全部）
- `--base-dir`: 数据保存目录（默认：financial_data_offline）

## 🔧 故障排除

### 常见问题

1. **API密钥错误**
   ```
   ❌ API调用失败: 401 Unauthorized
   ```
   解决方案：检查`.env`文件中的`FINANCIAL_DATASETS_API_KEY`是否正确

2. **网络连接问题**
   ```
   ❌ API调用失败: Connection timeout
   ```
   解决方案：检查网络连接，或稍后重试

3. **磁盘空间不足**
   ```
   ❌ 保存失败: No space left on device
   ```
   解决方案：清理磁盘空间，或更改保存目录

4. **权限问题**
   ```
   ❌ 目录创建失败: Permission denied
   ```
   解决方案：确保有写入权限，或使用管理员权限运行

### 数据验证
运行以下命令验证下载的数据：

```bash
# 检查数据文件数量
find financial_data_offline -name "*.json" | wc -l

# 检查特定股票的数据
ls -la financial_data_offline/GOOGL_*
ls -la financial_data_offline/TSLA_*

# 查看下载报告
cat financial_data_offline/download_report_*.txt
```

## 📈 性能优化建议

1. **批量下载**: 一次性下载所有需要的数据类型
2. **增量更新**: 定期运行脚本更新最新数据
3. **缓存利用**: 脚本会自动跳过已存在的数据文件
4. **并行处理**: 可以同时为多个股票下载数据

## 🔄 数据更新策略

### 定期更新
建议设置定期任务更新数据：

```bash
# 每周更新一次（Linux/Mac）
0 2 * * 1 /path/to/python /path/to/download_googl_tsla_financial_data.py

# Windows任务计划程序
# 创建每周一凌晨2点运行的任务
```

### 手动更新
在重要事件前手动更新数据：

```bash
# 财报季前更新
python download_googl_tsla_financial_data.py --data-types line_items financial_metrics

# 交易日前更新价格数据
python download_googl_tsla_financial_data.py --data-types prices market_cap
```

## 📞 支持

如果遇到问题，请：

1. 首先运行测试脚本诊断问题
2. 检查日志文件中的错误信息
3. 查看下载报告了解详细状态
4. 参考主README.md中的故障排除部分

---

**注意**: 本指南假设您已经完成了基本的环境设置和依赖安装。如果还没有，请先参考主README.md文件。
