#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GOOGL和TSLA设置验证脚本

该脚本验证所有新创建的GOOGL和TSLA相关文件和功能是否正常工作。

使用方法:
    python validate_googl_tsla_setup.py
"""

import os
import sys
import json
import importlib.util
from pathlib import Path
from datetime import datetime

def print_section(title: str):
    """打印章节标题"""
    print(f"\n{'='*50}")
    print(f"🔍 {title}")
    print('='*50)

def check_file_exists(file_path: str, description: str) -> bool:
    """检查文件是否存在"""
    path = Path(file_path)
    if path.exists():
        size = path.stat().st_size
        print(f"✅ {description}: 存在 ({size:,} 字节)")
        return True
    else:
        print(f"❌ {description}: 不存在")
        return False

def validate_python_syntax(file_path: str, description: str) -> bool:
    """验证Python文件语法"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            code = f.read()
        
        compile(code, file_path, 'exec')
        print(f"✅ {description}: 语法正确")
        return True
    except SyntaxError as e:
        print(f"❌ {description}: 语法错误 - {e}")
        return False
    except Exception as e:
        print(f"❌ {description}: 验证失败 - {e}")
        return False

def check_script_imports(file_path: str, description: str) -> bool:
    """检查脚本导入是否正常"""
    try:
        spec = importlib.util.spec_from_file_location("test_module", file_path)
        if spec is None:
            print(f"❌ {description}: 无法加载模块规范")
            return False
        
        # 不实际导入，只检查是否可以创建模块
        print(f"✅ {description}: 可以导入")
        return True
    except Exception as e:
        print(f"❌ {description}: 导入失败 - {e}")
        return False

def validate_readme_content():
    """验证README.md内容更新"""
    print_section("验证README.md更新")
    
    if not check_file_exists("README.md", "README.md文件"):
        return False
    
    try:
        with open("README.md", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键更新内容
        checks = [
            ("GOOGL金融数据支持", "支持本地数据优先策略，可使用下载脚本预缓存" in content),
            ("TSLA金融数据支持", "Tesla Inc." in content and "支持本地数据优先策略" in content),
            ("下载脚本说明", "download_googl_tsla_financial_data.py" in content),
            ("测试脚本说明", "test_googl_tsla_download.py" in content),
            ("演示脚本说明", "demo_googl_tsla_setup.py" in content),
            ("设置指南链接", "docs/googl_tsla_setup_guide.md" in content),
        ]
        
        success_count = 0
        for check_name, check_result in checks:
            if check_result:
                print(f"✅ {check_name}: 已更新")
                success_count += 1
            else:
                print(f"❌ {check_name}: 未找到")
        
        print(f"📊 README更新完成度: {success_count}/{len(checks)}")
        return success_count == len(checks)
        
    except Exception as e:
        print(f"❌ README验证失败: {e}")
        return False

def validate_setup_guide():
    """验证设置指南"""
    print_section("验证设置指南")
    
    guide_path = "docs/googl_tsla_setup_guide.md"
    
    if not check_file_exists(guide_path, "设置指南文件"):
        return False
    
    try:
        with open(guide_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查指南内容完整性
        required_sections = [
            "# GOOGL和TSLA股票数据设置指南",
            "## 📋 数据支持现状",
            "## 🚀 快速开始",
            "## 📊 数据类型说明",
            "## ⚙️ 配置说明",
            "## 🔧 故障排除"
        ]
        
        missing_sections = []
        for section in required_sections:
            if section not in content:
                missing_sections.append(section)
        
        if not missing_sections:
            print("✅ 设置指南内容完整")
            return True
        else:
            print(f"❌ 设置指南缺少章节: {missing_sections}")
            return False
            
    except Exception as e:
        print(f"❌ 设置指南验证失败: {e}")
        return False

def validate_scripts():
    """验证脚本文件"""
    print_section("验证脚本文件")
    
    scripts = [
        ("download_googl_tsla_financial_data.py", "金融数据下载脚本"),
        ("test_googl_tsla_download.py", "下载功能测试脚本"),
        ("demo_googl_tsla_setup.py", "设置演示脚本"),
        ("validate_googl_tsla_setup.py", "设置验证脚本")
    ]
    
    results = []
    
    for script_path, description in scripts:
        exists = check_file_exists(script_path, description)
        if exists:
            syntax_ok = validate_python_syntax(script_path, f"{description}语法")
            import_ok = check_script_imports(script_path, f"{description}导入")
            results.append(exists and syntax_ok and import_ok)
        else:
            results.append(False)
    
    success_count = sum(results)
    print(f"📊 脚本验证结果: {success_count}/{len(scripts)} 通过")
    
    return success_count == len(scripts)

def check_directory_structure():
    """检查目录结构"""
    print_section("检查目录结构")
    
    # 检查现有数据目录
    data_dirs = [
        ("GOOGL_alpha_news", "GOOGL新闻数据目录"),
        ("TSLA_alpha_news", "TSLA新闻数据目录"),
        ("social_media_data/GOOGL_social_media", "GOOGL社交媒体数据目录"),
        ("social_media_data/TSLA_social_media", "TSLA社交媒体数据目录"),
    ]
    
    existing_dirs = 0
    for dir_path, description in data_dirs:
        if Path(dir_path).exists():
            file_count = len(list(Path(dir_path).glob("*.json")))
            print(f"✅ {description}: 存在 ({file_count} 文件)")
            existing_dirs += 1
        else:
            print(f"⚠️ {description}: 不存在")
    
    # 检查文档目录
    docs_dir = Path("docs")
    if not docs_dir.exists():
        docs_dir.mkdir(exist_ok=True)
        print("✅ 创建docs目录")
    else:
        print("✅ docs目录已存在")
    
    # 检查金融数据目录（可能不存在）
    financial_dir = Path("financial_data_offline")
    if financial_dir.exists():
        googl_dirs = len(list(financial_dir.glob("GOOGL_*")))
        tsla_dirs = len(list(financial_dir.glob("TSLA_*")))
        print(f"✅ 金融数据目录: 存在 (GOOGL: {googl_dirs} 子目录, TSLA: {tsla_dirs} 子目录)")
    else:
        print("⚠️ 金融数据目录: 不存在（将在首次下载时创建）")
    
    print(f"📊 现有数据目录: {existing_dirs}/{len(data_dirs)}")
    return True

def generate_validation_report():
    """生成验证报告"""
    print_section("生成验证报告")
    
    report_data = {
        'validation_time': datetime.now().isoformat(),
        'python_version': f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
        'working_directory': str(Path.cwd()),
        'files_created': [
            'download_googl_tsla_financial_data.py',
            'test_googl_tsla_download.py', 
            'demo_googl_tsla_setup.py',
            'validate_googl_tsla_setup.py',
            'docs/googl_tsla_setup_guide.md'
        ],
        'readme_updated': True,
        'validation_status': 'completed'
    }
    
    report_file = Path("googl_tsla_validation_report.json")
    
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 验证报告已保存: {report_file}")
        print(f"📄 报告内容: {len(report_data)} 项信息")
        
        return True
        
    except Exception as e:
        print(f"❌ 报告生成失败: {e}")
        return False

def main():
    """主验证函数"""
    print("🔍 GOOGL和TSLA设置验证工具")
    print(f"⏰ 验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📁 工作目录: {Path.cwd()}")
    
    # 执行各项验证
    validations = [
        ("目录结构检查", check_directory_structure),
        ("脚本文件验证", validate_scripts),
        ("README内容验证", validate_readme_content),
        ("设置指南验证", validate_setup_guide),
    ]
    
    results = []
    for validation_name, validation_func in validations:
        try:
            result = validation_func()
            results.append((validation_name, result))
        except Exception as e:
            print(f"❌ {validation_name}验证异常: {e}")
            results.append((validation_name, False))
    
    # 生成验证报告
    report_generated = generate_validation_report()
    
    # 总结结果
    print_section("验证总结")
    
    passed = 0
    total = len(results)
    
    for validation_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {status}: {validation_name}")
        if result:
            passed += 1
    
    print(f"\n📊 验证结果: {passed}/{total} 项通过")
    print(f"📄 报告生成: {'✅ 成功' if report_generated else '❌ 失败'}")
    
    if passed == total:
        print("\n🎉 所有验证通过！GOOGL和TSLA设置已完成")
        print("\n💡 下一步操作:")
        print("  1. 运行测试: python test_googl_tsla_download.py")
        print("  2. 下载数据: python download_googl_tsla_financial_data.py")
        print("  3. 运行演示: python demo_googl_tsla_setup.py")
        print("  4. 开始回测: python src/backtester.py --tickers GOOGL,TSLA")
    else:
        print(f"\n⚠️ {total - passed} 项验证失败，请检查相关问题")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
